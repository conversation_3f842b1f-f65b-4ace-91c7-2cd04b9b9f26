# Webhook Queue System Design
## Cross-Platform Duplicate Prevention for DermaCare

### Executive Summary

This document outlines the design for a robust webhook queue system that prevents duplicate processing across AutoPatient (AP) and CliniCore (CC) platforms. The system addresses race conditions, provides reliable processing with retry mechanisms, and includes comprehensive monitoring and observability.

### Current State Analysis

The existing system processes webhooks directly without queuing:
- Direct webhook processing in `ccWebhookHandler` and `apContactWebhookHandler`
- Sync buffer logic (60-second window) to prevent processing loops
- Cross-platform patient/appointment relationship tracking via database
- Fire-and-forget custom field synchronization
- Previous queue system was removed due to complexity issues

### Requirements

1. **Cross-Platform Duplicate Prevention**: Detect and prevent duplicate webhook processing when the same patient/appointment is modified on both platforms simultaneously
2. **Queue Architecture**: Robust queuing with retry mechanisms, error handling, and sequential processing
3. **Duplicate Detection Strategy**: Intelligent detection across platforms using patient/appointment relationships
4. **Implementation Plan**: Phased rollout with comprehensive testing and monitoring

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐
│   AutoPatient   │    │   CliniCore     │
│    Webhooks     │    │    Webhooks     │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────────────────────────────┐
│           Webhook Queue API             │
│  ┌─────────────────────────────────────┐│
│  │        Duplicate Detection          ││
│  │   ┌─────────────────────────────┐   ││
│  │   │   Cross-Platform Mapping    │   ││
│  │   │   Timing-Based Detection    │   ││
│  │   │   Content-Based Detection   │   ││
│  │   └─────────────────────────────┘   ││
│  └─────────────────────────────────────┘│
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│            Webhook Queue                │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │Pending  │ │Processing│ │Completed│   │
│  │Webhooks │ │Webhooks  │ │Webhooks │   │
│  └─────────┘ └─────────┘ └─────────┘   │
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│          Queue Processor                │
│  ┌─────────────────────────────────────┐│
│  │     Sequential Processing           ││
│  │     Retry Logic                     ││
│  │     Error Handling                  ││
│  │     Performance Monitoring          ││
│  └─────────────────────────────────────┘│
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│       Existing Webhook Processors      │
│  ┌─────────────────┐ ┌─────────────────┐│
│  │  CC Webhook     │ │  AP Webhook     ││
│  │  Processor      │ │  Processor      ││
│  └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────┘
```

## Database Schema

### 1. webhook_queue Table

```sql
CREATE TABLE webhook_queue (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
    source VARCHAR(10) NOT NULL CHECK (source IN ('ap', 'cc')),
    event_type VARCHAR(100) NOT NULL, -- 'patient_created', 'patient_updated', etc.
    entity_type VARCHAR(50) NOT NULL CHECK (entity_type IN ('patient', 'appointment')),
    entity_id VARCHAR(255) NOT NULL, -- Source platform's entity ID
    related_patient_id VARCHAR(255), -- Our internal patient ID for cross-platform linking
    related_appointment_id VARCHAR(255), -- Our internal appointment ID
    payload JSONB NOT NULL, -- Full webhook payload
    status VARCHAR(20) NOT NULL DEFAULT 'pending' 
        CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'duplicate_skipped')),
    priority INTEGER NOT NULL DEFAULT 0, -- Higher number = higher priority
    retry_count INTEGER NOT NULL DEFAULT 0,
    max_retries INTEGER NOT NULL DEFAULT 3,
    scheduled_at TIMESTAMP NOT NULL DEFAULT NOW(), -- For delayed processing
    started_at TIMESTAMP, -- When processing began
    completed_at TIMESTAMP, -- When processing finished
    error_message TEXT, -- Last error if failed
    duplicate_of VARCHAR(255), -- Reference to original webhook if duplicate
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- Foreign key constraints
    FOREIGN KEY (related_patient_id) REFERENCES patients(id),
    FOREIGN KEY (related_appointment_id) REFERENCES appointments(id),
    FOREIGN KEY (duplicate_of) REFERENCES webhook_queue(id)
);

-- Indexes for performance
CREATE INDEX idx_webhook_queue_status_priority ON webhook_queue(status, priority DESC, created_at ASC);
CREATE INDEX idx_webhook_queue_entity ON webhook_queue(entity_type, entity_id, source);
CREATE INDEX idx_webhook_queue_patient ON webhook_queue(related_patient_id, created_at DESC);
CREATE INDEX idx_webhook_queue_scheduled ON webhook_queue(scheduled_at) WHERE status = 'pending';
```

### 2. webhook_queue_logs Table

```sql
CREATE TABLE webhook_queue_logs (
    -- Same structure as webhook_queue
    id VARCHAR(255) PRIMARY KEY,
    source VARCHAR(10) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(255) NOT NULL,
    related_patient_id VARCHAR(255),
    related_appointment_id VARCHAR(255),
    payload JSONB NOT NULL,
    final_status VARCHAR(20) NOT NULL,
    priority INTEGER NOT NULL,
    retry_count INTEGER NOT NULL,
    max_retries INTEGER NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP NOT NULL,
    error_message TEXT,
    duplicate_of VARCHAR(255),
    processing_duration_ms INTEGER, -- Total processing time
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    archived_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Indexes for audit queries
CREATE INDEX idx_webhook_logs_entity ON webhook_queue_logs(entity_type, entity_id, source);
CREATE INDEX idx_webhook_logs_status ON webhook_queue_logs(final_status, archived_at DESC);
```

### 3. duplicate_prevention_locks Table

```sql
CREATE TABLE duplicate_prevention_locks (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type VARCHAR(50) NOT NULL,
    entity_key VARCHAR(500) NOT NULL, -- Composite key for cross-platform detection
    locked_until TIMESTAMP NOT NULL,
    webhook_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    FOREIGN KEY (webhook_id) REFERENCES webhook_queue(id),
    UNIQUE(entity_type, entity_key)
);

-- TTL index for automatic cleanup
CREATE INDEX idx_duplicate_locks_ttl ON duplicate_prevention_locks(locked_until);
```

## Duplicate Detection Strategy

### Cross-Platform Entity Mapping

```
Patient Detection Flow:
1. AP webhook arrives with contact_id "123"
2. Look up patients table: apId="123" → internal patient_id="uuid-456"
3. Check webhook_queue for CC webhooks with related_patient_id="uuid-456" 
   within duplicate window (30 seconds)
4. If found: Mark as duplicate, link to original
5. If not found: Process normally

Appointment Detection Flow:
1. Similar logic for appointments using appointment table
2. Also check for patient-level webhooks (appointment changes often trigger patient updates)
3. Cross-reference both patient and appointment relationships
```

### Duplicate Detection Algorithm

```typescript
interface DuplicateDetectionResult {
    isDuplicate: boolean;
    originalWebhookId?: string;
    reason: string;
    confidence: 'high' | 'medium' | 'low';
}

async function detectDuplicate(webhook: WebhookQueueItem): Promise<DuplicateDetectionResult> {
    // Layer 1: Entity-based detection
    const entityDuplicates = await findEntityDuplicates(webhook);
    if (entityDuplicates.length > 0) {
        return {
            isDuplicate: true,
            originalWebhookId: entityDuplicates[0].id,
            reason: 'Same entity modified across platforms',
            confidence: 'high'
        };
    }
    
    // Layer 2: Timing-based detection
    const timingDuplicates = await findTimingDuplicates(webhook);
    if (timingDuplicates.length > 0) {
        return {
            isDuplicate: true,
            originalWebhookId: timingDuplicates[0].id,
            reason: 'Similar changes within duplicate window',
            confidence: 'medium'
        };
    }
    
    // Layer 3: Content-based detection (future enhancement)
    // Compare payload content for similar field changes
    
    return {
        isDuplicate: false,
        reason: 'No duplicates detected',
        confidence: 'high'
    };
}
```

## Queue Processing Architecture

### Processing Flow

```
┌─────────────────┐
│  Queue Worker   │
│   (Cron Job)    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 1. Poll Queue   │ ← ORDER BY priority DESC, created_at ASC
│   Get Next      │   WHERE status = 'pending'
│   Pending       │   AND scheduled_at <= NOW()
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 2. Acquire Lock │ ← UPDATE status = 'processing'
│   Set Processing│   SET started_at = NOW()
│   Status        │   WHERE id = ? AND status = 'pending'
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 3. Duplicate    │ ← Check cross-platform relationships
│   Detection     │   Check timing windows
│                 │   Check content similarity
└─────────┬───────┘
          │
    ┌─────▼─────┐
    │ Duplicate?│
    └─────┬─────┘
          │
    ┌─────▼─────┐         ┌─────────────────┐
    │    Yes    │────────▶│ 4a. Mark as     │
    │           │         │    Duplicate    │
    └───────────┘         │    Skip         │
                          └─────────┬───────┘
    ┌─────▼─────┐                   │
    │    No     │                   │
    │           │                   │
    └─────┬─────┘                   │
          │                         │
          ▼                         │
┌─────────────────┐                 │
│ 4b. Process     │                 │
│    Webhook      │ ← Call existing │
│    (Call        │   webhook       │
│    Existing     │   processors    │
│    Processors)  │                 │
└─────────┬───────┘                 │
          │                         │
    ┌─────▼─────┐                   │
    │ Success?  │                   │
    └─────┬─────┘                   │
          │                         │
    ┌─────▼─────┐         ┌─────────▼───────┐
    │    Yes    │────────▶│ 5a. Mark        │
    │           │         │    Completed    │
    └───────────┘         │    Move to Logs │
                          └─────────────────┘
    ┌─────▼─────┐
    │    No     │
    │           │
    └─────┬─────┘
          │
          ▼
┌─────────────────┐
│ 5b. Handle      │
│    Failure      │
└─────────┬───────┘
          │
    ┌─────▼─────┐
    │ Retries   │
    │ < Max?    │
    └─────┬─────┘
          │
    ┌─────▼─────┐         ┌─────────────────┐
    │    Yes    │────────▶│ 6a. Increment   │
    │           │         │    Retry Count  │
    └───────────┘         │    Reset to     │
                          │    Pending      │
                          └─────────────────┘
    ┌─────▼─────┐
    │    No     │
    │           │
    └─────┬─────┘
          │
          ▼
┌─────────────────┐
│ 6b. Mark Failed │
│    Move to Logs │
└─────────────────┘
```

### Background Processing Implementation

```typescript
// Cloudflare Workers Cron Trigger
export default {
    async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
        const queueProcessor = new WebhookQueueProcessor(env);

        // Process webhooks with timeout
        const processingPromise = queueProcessor.processQueue({
            batchSize: 10,
            timeoutMs: 25000 // Leave 5s buffer for Cloudflare Workers timeout
        });

        // Use waitUntil to ensure processing completes
        ctx.waitUntil(processingPromise);
    }
};

// Cron schedule in wrangler.toml
[triggers]
crons = ["*/30 * * * *"] // Every 30 seconds
```

### API Endpoints

#### 1. Queue Management

```typescript
// Add webhook to queue
POST /api/queue/webhook
{
    "source": "ap" | "cc",
    "event_type": "patient_created" | "patient_updated" | "appointment_created" | "appointment_updated",
    "entity_type": "patient" | "appointment",
    "entity_id": "string",
    "payload": object,
    "priority": number, // Optional, default 0
    "scheduled_at": "ISO timestamp" // Optional, default NOW()
}

// Get queue status and metrics
GET /api/queue/status
Response: {
    "queue_depth": number,
    "processing_rate": number, // webhooks per minute
    "success_rate": number, // percentage
    "duplicate_rate": number, // percentage
    "average_processing_time_ms": number,
    "oldest_pending_webhook": "ISO timestamp",
    "worker_status": "active" | "idle" | "error"
}

// Get specific webhook details
GET /api/queue/webhooks/:id
Response: {
    "id": "string",
    "source": "ap" | "cc",
    "event_type": "string",
    "status": "pending" | "processing" | "completed" | "failed" | "duplicate_skipped",
    "retry_count": number,
    "created_at": "ISO timestamp",
    "started_at": "ISO timestamp",
    "completed_at": "ISO timestamp",
    "error_message": "string",
    "duplicate_of": "string"
}

// Manually retry failed webhook
POST /api/queue/retry/:id
Response: {
    "success": boolean,
    "message": "string"
}
```

## Error Handling Strategy

### Error Classification

```typescript
enum ErrorType {
    TRANSIENT = 'transient',     // Network timeouts, API rate limits
    PERMANENT = 'permanent',     // Invalid payload, auth failures
    DUPLICATE = 'duplicate',     // Duplicate detection errors
    PROCESSING = 'processing'    // Business logic errors
}

interface ErrorHandlingConfig {
    transient: {
        maxRetries: 3,
        backoffStrategy: 'exponential', // 30s, 2m, 10m
        retryableStatusCodes: [408, 429, 500, 502, 503, 504]
    },
    permanent: {
        maxRetries: 0,
        alerting: true
    },
    duplicate: {
        maxRetries: 1,
        fallbackToProcessing: true // Process as non-duplicate if detection fails
    },
    processing: {
        maxRetries: 2,
        backoffStrategy: 'linear' // 1m, 2m
    }
}
```

### Retry Logic

```typescript
async function calculateNextRetry(webhook: WebhookQueueItem, error: ProcessingError): Promise<Date> {
    const baseDelays = {
        transient: [30, 120, 600], // 30s, 2m, 10m
        processing: [60, 120],     // 1m, 2m
        duplicate: [30]            // 30s
    };

    const delays = baseDelays[error.type] || baseDelays.transient;
    const retryIndex = Math.min(webhook.retry_count, delays.length - 1);
    const delaySeconds = delays[retryIndex];

    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delaySeconds;
    const totalDelay = delaySeconds + jitter;

    return new Date(Date.now() + totalDelay * 1000);
}
```

## Monitoring and Observability

### Key Metrics

```typescript
interface QueueMetrics {
    // Queue Health
    queue_depth: number;                    // Current pending webhooks
    processing_rate: number;                // Webhooks processed per minute
    average_processing_time_ms: number;     // Average time to process
    oldest_pending_age_minutes: number;     // Age of oldest pending webhook

    // Success Rates
    success_rate_1h: number;               // Success rate last hour
    success_rate_24h: number;              // Success rate last 24 hours
    duplicate_detection_rate: number;      // Percentage of duplicates detected

    // Error Rates
    failure_rate_by_type: {
        transient: number;
        permanent: number;
        processing: number;
        duplicate: number;
    };

    // Retry Statistics
    average_retries_per_webhook: number;
    retry_success_rate: number;

    // Platform Distribution
    webhook_distribution: {
        ap: number;
        cc: number;
    };

    // Entity Distribution
    entity_distribution: {
        patient: number;
        appointment: number;
    };
}
```

### Alerting Rules

```yaml
alerts:
  - name: "High Queue Depth"
    condition: "queue_depth > 100"
    severity: "warning"
    description: "Webhook queue has more than 100 pending items"

  - name: "Processing Stalled"
    condition: "no webhooks processed in 5 minutes AND queue_depth > 0"
    severity: "critical"
    description: "Queue processing appears to be stalled"

  - name: "High Failure Rate"
    condition: "failure_rate_1h > 10%"
    severity: "warning"
    description: "Webhook processing failure rate exceeds 10%"

  - name: "Duplicate Detection Issues"
    condition: "duplicate_detection_rate > 50% OR duplicate_detection_rate < 1%"
    severity: "warning"
    description: "Duplicate detection rate is outside normal range"

  - name: "Old Pending Webhooks"
    condition: "oldest_pending_age_minutes > 30"
    severity: "warning"
    description: "Webhooks have been pending for more than 30 minutes"
```

### Logging Strategy

```typescript
interface WebhookProcessingLog {
    webhook_id: string;
    correlation_id: string;
    timestamp: string;
    level: 'debug' | 'info' | 'warn' | 'error';
    stage: 'queued' | 'processing' | 'duplicate_check' | 'completed' | 'failed';
    message: string;
    metadata: {
        source: 'ap' | 'cc';
        entity_type: 'patient' | 'appointment';
        entity_id: string;
        processing_time_ms?: number;
        retry_count?: number;
        error_type?: string;
        duplicate_of?: string;
    };
}

// Example log entries
{
    "webhook_id": "uuid-123",
    "correlation_id": "req-456",
    "timestamp": "2024-08-06T10:30:00.000Z",
    "level": "info",
    "stage": "duplicate_check",
    "message": "Duplicate detected for patient webhook",
    "metadata": {
        "source": "cc",
        "entity_type": "patient",
        "entity_id": "789",
        "duplicate_of": "uuid-456",
        "detection_reason": "Same entity modified across platforms",
        "confidence": "high"
    }
}
```

## Implementation Plan

### Phase 1: Core Queue Infrastructure (Week 1)

**Deliverables:**
- Database schema creation and migrations
- Basic queue operations (add, get, update status)
- Simple webhook processor that calls existing handlers
- Basic API endpoints for queue management
- Unit tests for core functionality

**Tasks:**
1. Create database migrations for `webhook_queue`, `webhook_queue_logs`, and `duplicate_prevention_locks` tables
2. Implement `WebhookQueueService` class with CRUD operations
3. Create `QueueProcessor` class that calls existing webhook handlers
4. Build API endpoints: `POST /api/queue/webhook`, `GET /api/queue/status`
5. Write unit tests for queue operations
6. Set up basic monitoring and logging

**Success Criteria:**
- Webhooks can be added to queue and processed sequentially
- Basic queue status reporting works
- All tests pass
- No performance degradation from existing system

### Phase 2: Duplicate Detection (Week 2)

**Deliverables:**
- Cross-platform entity relationship mapping
- Duplicate detection algorithms
- Timing-based duplicate prevention
- Integration tests for duplicate scenarios
- Performance optimization for detection queries

**Tasks:**
1. Implement `DuplicateDetectionService` with entity-based detection
2. Add timing-based duplicate detection with configurable windows
3. Create cross-platform relationship mapping utilities
4. Build comprehensive integration tests for duplicate scenarios
5. Optimize database queries with proper indexing
6. Add duplicate detection metrics and logging

**Success Criteria:**
- Duplicate detection accuracy > 95% for same-entity modifications
- Detection latency < 100ms for 99% of requests
- No false positives in integration tests
- Comprehensive logging for all duplicate decisions

### Phase 3: Advanced Processing (Week 3)

**Deliverables:**
- Retry mechanisms with exponential backoff
- Error handling and classification
- Queue prioritization and scheduling
- Background worker implementation
- Monitoring and metrics collection

**Tasks:**
1. Implement retry logic with configurable backoff strategies
2. Add error classification and handling for different error types
3. Build queue prioritization system
4. Create Cloudflare Workers cron trigger for background processing
5. Implement comprehensive metrics collection
6. Add alerting rules and monitoring dashboards

**Success Criteria:**
- Retry success rate > 80% for transient errors
- Queue processing latency < 30 seconds for 95% of webhooks
- Comprehensive error classification and handling
- Real-time monitoring and alerting working

### Phase 4: Production Readiness (Week 4)

**Deliverables:**
- Comprehensive testing with real webhook data
- Performance testing and optimization
- Documentation and runbooks
- Deployment automation
- Monitoring dashboards and alerting

**Tasks:**
1. Conduct load testing with realistic webhook volumes
2. Performance optimization based on testing results
3. Create operational documentation and runbooks
4. Set up deployment automation and CI/CD
5. Build monitoring dashboards
6. Conduct security review and penetration testing

**Success Criteria:**
- System handles 1000+ webhooks/hour without degradation
- All documentation complete and reviewed
- Deployment automation working
- Security review passed
- Monitoring and alerting fully operational

### Migration Strategy

```
Phase A: Parallel Deployment (Week 5)
┌─────────────────┐    ┌─────────────────┐
│   Webhook       │    │   Webhook       │
│   Endpoints     │    │   Endpoints     │
│   (Current)     │    │   (Current)     │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│   Direct        │    │   Queue         │
│   Processing    │    │   Processing    │
│   (90%)         │    │   (10%)         │
└─────────────────┘    └─────────────────┘

Phase B: Gradual Migration (Week 6-7)
- Increase queue processing to 25%, then 50%, then 75%
- Monitor metrics and error rates at each step
- Rollback capability at each phase

Phase C: Full Cutover (Week 8)
- Route 100% of webhooks through queue
- Remove direct processing code
- Monitor for 1 week before declaring success
```

## Configuration Management

### Environment Configuration

```typescript
interface QueueConfig {
    // Processing Configuration
    processing: {
        batchSize: number;              // Default: 10
        timeoutMs: number;              // Default: 25000
        cronSchedule: string;           // Default: "*/30 * * * *"
        maxConcurrentWorkers: number;   // Default: 1
    };

    // Duplicate Detection Configuration
    duplicateDetection: {
        enabled: boolean;               // Default: true
        windowSeconds: number;          // Default: 30
        contentSimilarityThreshold: number; // Default: 0.8
        crossPlatformEnabled: boolean;  // Default: true
    };

    // Retry Configuration
    retry: {
        maxRetries: number;             // Default: 3
        backoffStrategy: 'exponential' | 'linear'; // Default: 'exponential'
        baseDelaySeconds: number;       // Default: 30
        maxDelaySeconds: number;        // Default: 600
        jitterPercent: number;          // Default: 10
    };

    // Monitoring Configuration
    monitoring: {
        metricsEnabled: boolean;        // Default: true
        alertingEnabled: boolean;       // Default: true
        logLevel: 'debug' | 'info' | 'warn' | 'error'; // Default: 'info'
        retentionDays: number;          // Default: 30
    };

    // Feature Flags
    features: {
        queueEnabled: boolean;          // Default: false (for gradual rollout)
        queuePercentage: number;        // Default: 0 (percentage of traffic to queue)
        contentBasedDetection: boolean; // Default: false (future feature)
        priorityProcessing: boolean;    // Default: true
    };
}
```

### Security Considerations

```typescript
interface SecurityConfig {
    // API Security
    api: {
        rateLimiting: {
            enabled: boolean;
            requestsPerMinute: number;  // Default: 100
            burstLimit: number;         // Default: 20
        };
        authentication: {
            required: boolean;          // Default: true
            apiKeyHeader: string;       // Default: 'X-API-Key'
            allowedOrigins: string[];   // CORS configuration
        };
    };

    // Data Security
    data: {
        encryptPayloads: boolean;       // Default: false (future feature)
        sanitizeLogging: boolean;       // Default: true
        auditTrail: boolean;           // Default: true
        dataRetentionDays: number;     // Default: 90
    };

    // Access Control
    access: {
        adminEndpoints: string[];       // Endpoints requiring admin access
        readOnlyEndpoints: string[];    // Endpoints for monitoring access
        ipWhitelist: string[];         // Optional IP restrictions
    };
}
```

### Performance Considerations

```typescript
interface PerformanceConfig {
    // Database Configuration
    database: {
        connectionPoolSize: number;     // Default: 10
        queryTimeoutMs: number;         // Default: 5000
        indexOptimization: boolean;     // Default: true
        batchInsertSize: number;        // Default: 100
    };

    // Caching Configuration
    caching: {
        enabled: boolean;               // Default: true
        duplicateDetectionCacheTtl: number; // Default: 300 seconds
        entityLookupCacheTtl: number;   // Default: 60 seconds
        metricsCacheTtl: number;        // Default: 30 seconds
    };

    // Resource Limits
    limits: {
        maxQueueDepth: number;          // Default: 10000
        maxPayloadSizeKb: number;       // Default: 1024
        maxProcessingTimeMs: number;    // Default: 30000
        maxRetryDelayMs: number;        // Default: 600000
    };
}
```

## Disaster Recovery

### Backup and Recovery Procedures

```yaml
backup_strategy:
  database:
    frequency: "daily"
    retention: "30 days"
    tables:
      - webhook_queue
      - webhook_queue_logs
      - duplicate_prevention_locks

  configuration:
    frequency: "on_change"
    retention: "indefinite"
    includes:
      - environment_variables
      - feature_flags
      - monitoring_rules

recovery_procedures:
  queue_corruption:
    steps:
      1. "Stop queue processing"
      2. "Restore from latest backup"
      3. "Replay missed webhooks from logs"
      4. "Resume processing"
    rto: "15 minutes"
    rpo: "1 hour"

  processing_failure:
    steps:
      1. "Identify failed webhooks"
      2. "Reset status to pending"
      3. "Increase retry limits if needed"
      4. "Monitor recovery progress"
    rto: "5 minutes"
    rpo: "0 minutes"
```

### Manual Intervention Procedures

```sql
-- Reset stuck webhooks
UPDATE webhook_queue
SET status = 'pending', retry_count = 0, started_at = NULL
WHERE status = 'processing'
AND started_at < NOW() - INTERVAL '30 minutes';

-- Manually retry failed webhook
UPDATE webhook_queue
SET status = 'pending', retry_count = 0, error_message = NULL
WHERE id = 'webhook-id-here';

-- Clear duplicate prevention locks
DELETE FROM duplicate_prevention_locks
WHERE locked_until < NOW();

-- Emergency queue drain (process all pending)
UPDATE webhook_queue
SET priority = 999
WHERE status = 'pending';
```

## Conclusion

This webhook queue system design provides a robust, scalable solution for cross-platform duplicate prevention in the DermaCare synchronization system. The phased implementation approach ensures minimal risk while delivering comprehensive functionality including:

- **Reliable Processing**: Sequential processing with retry mechanisms and error handling
- **Duplicate Prevention**: Multi-layered detection across AutoPatient and CliniCore platforms
- **Monitoring**: Comprehensive metrics, alerting, and observability
- **Scalability**: Designed to handle high webhook volumes with performance optimization
- **Maintainability**: Clean architecture with proper separation of concerns

The system builds upon existing infrastructure while providing significant improvements in reliability and duplicate prevention capabilities.
