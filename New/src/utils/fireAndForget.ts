/**
 * Fire-and-Forget HTTP Request Utility
 *
 * Provides a standardized way to make HTTP requests that don't require waiting
 * for the response. Ideal for triggering background processes, webhooks, or
 * notifications where the main request flow shouldn't be blocked.
 *
 * Features:
 * - Fire-and-forget pattern (no awaiting required)
 * - Automatic error handling with logging
 * - Request/response logging for debugging
 * - Flexible configuration options
 * - TypeScript support with proper types
 * - Automatic JSON serialization for object bodies
 *
 * @fileoverview Fire-and-forget HTTP request utility
 * @version 1.0.0
 * @since 2025-08-05
 */

import { getContext } from "hono/context-storage";
import { logDebug, logError, logInfo } from "@/utils/logger";

/**
 * Configuration options for fire-and-forget HTTP requests
 */
export interface FireAndForgetOptions {
	/** HTTP method (default: "GET") */
	method?: string;
	/** Request headers */
	headers?: Record<string, string>;
	/** Request body (will be JSON.stringify'd if object) */
	body?: string | object;
	/** Additional context for logging */
	logContext?: Record<string, unknown>;
	/** Whether to log the request attempt (default: true) */
	logRequest?: boolean;
	/** Whether to log successful responses (default: false) */
	logSuccess?: boolean;
	/** Whether to log errors (default: true) */
	logErrors?: boolean;
}

/**
 * Make a fire-and-forget HTTP request
 *
 * Sends an HTTP request without waiting for the response. The request is
 * executed asynchronously and any errors are caught and logged. This is
 * perfect for triggering background processes or sending notifications
 * where the main request flow shouldn't be blocked.
 *
 * **Implementation Details:**
 * - Uses standard fetch() API
 * - Automatically handles JSON serialization for object bodies
 * - Provides comprehensive error logging with request details
 * - Logs request attempts for debugging (configurable)
 * - Catches all errors to prevent unhandled promise rejections
 *
 * @param url - Target URL for the HTTP request
 * @param options - Configuration options for the request
 *
 * @example
 * ```typescript
 * // Simple GET request
 * fireAndForgetRequest('https://api.example.com/webhook');
 *
 * // POST request with JSON body
 * fireAndForgetRequest('https://api.example.com/trigger', {
 *   method: 'POST',
 *   headers: { 'Content-Type': 'application/json' },
 *   body: { action: 'process', id: '123' },
 *   logContext: { webhookId: '456', source: 'cc' }
 * });
 *
 * // Background API call
 * fireAndForgetRequest(`${baseUrl}/api/notify`, {
 *   method: 'POST',
 *   headers: {
 *     'Content-Type': 'application/json',
 *     'User-Agent': 'BackgroundNotifier/1.0'
 *   },
 *   logContext: { userId: '123', action: 'notify' }
 * });
 * ```
 *
 * @since 1.0.0
 */
export function fireAndForgetRequest(
	url: string,
	options: FireAndForgetOptions = {},
): void {
	const {
		method = "GET",
		headers = {},
		body,
		logContext = {},
		logRequest = true,
		logSuccess = false,
		logErrors = true,
	} = options;

	// Prepare request configuration
	const fetchOptions: RequestInit = {
		method,
		headers,
	};

	// Handle body serialization
	if (body !== undefined) {
		if (typeof body === "object") {
			fetchOptions.body = JSON.stringify(body);
			// Auto-add Content-Type for JSON bodies if not specified
			if (!headers["Content-Type"] && !headers["content-type"]) {
				fetchOptions.headers = {
					...headers,
					"Content-Type": "application/json",
				};
			}
		} else {
			fetchOptions.body = body;
		}
	}

	// Log request attempt if enabled
	if (logRequest) {
		logDebug("Fire-and-forget request initiated", {
			url,
			method,
			headers: fetchOptions.headers,
			hasBody: !!body,
			...logContext,
		});
	}

	// Execute the request with error handling
	fetch(url, fetchOptions)
		.then((response) => {
			if (logSuccess || !response.ok) {
				const logLevel = response.ok ? "debug" : "warn";
				const message = response.ok
					? "Fire-and-forget request completed successfully"
					: "Fire-and-forget request returned non-2xx status";

				const logData = {
					url,
					method,
					status: response.status,
					statusText: response.statusText,
					...logContext,
				};

				if (logLevel === "debug" && logSuccess) {
					logDebug(message, logData);
				} else if (logLevel === "warn") {
					logInfo(message, logData); // Use INFO for non-2xx to avoid spam
				}
			}
		})
		.catch((error) => {
			if (logErrors) {
				logError("Fire-and-forget request failed", {
					url,
					method,
					error: error instanceof Error ? error.message : String(error),
					stack: error instanceof Error ? error.stack : undefined,
					...logContext,
				});
			}
		});
}



/**
 * Convenience function for fire-and-forget custom field sync triggers
 *
 * Pre-configured for triggering custom field synchronization endpoints.
 * Automatically extracts the base URL from the Hono context object.
 *
 * @param c - Hono context object containing request information
 * @param patientId - Patient ID for sync
 * @param platform - Target platform ("ap" or "cc")
 * @param context - Additional context for logging
 *
 * @example
 * ```typescript
 * // Trigger custom field sync
 * fireAndForgetCustomFieldSync(c, 'patient-123', 'cc', {
 *   requestId: 'req-456'
 * });
 * ```
 *
 * @since 1.0.0
 */
export function fireAndForgetCustomFieldSync(
	patientId: string,
	platform: "ap" | "cc",
	context: Record<string, unknown> = {},
): void {
	console.log("🔄 CUSTOM FIELD SYNC: Function called", {
		patientId,
		platform,
		context,
		timestamp: new Date().toISOString(),
	});

	// Get the current request URL and execution context
	let baseUrl: string;
	let executionContext: ExecutionContext | null = null;

	try {
		const ctx = getContext<Env>();
		baseUrl = new URL(ctx.req.url).origin;
		// Try to get execution context from Hono context
		executionContext = ctx.executionCtx || null;
		console.log("🔄 CUSTOM FIELD SYNC: Using context URL", {
			baseUrl,
			hasExecutionContext: !!executionContext,
		});
	} catch {
		// Fallback to production URL if context is not available
		baseUrl = "https://dermacare-sync.try.workers.dev";
		console.log("🔄 CUSTOM FIELD SYNC: Using fallback URL", { baseUrl });
	}

	const endpoint = `/admin/custom-fields-sync/${patientId}/${platform}?skip=true`;
	const fullUrl = `${baseUrl}${endpoint}`;

	console.log("🔄 CUSTOM FIELD SYNC: Creating fetch promise", {
		fullUrl,
		patientId,
		platform,
	});

	// Create the fetch promise
	const fetchPromise = fetch(fullUrl, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			"User-Agent": "CustomFieldSync-Chain/1.0",
		},
	})
		.then((response) => {
			console.log("🔄 CUSTOM FIELD SYNC: Fetch response received", {
				url: fullUrl,
				status: response.status,
				statusText: response.statusText,
				ok: response.ok,
			});
			return response;
		})
		.catch((error) => {
			console.error("🔄 CUSTOM FIELD SYNC: Fetch error", {
				url: fullUrl,
				error: error instanceof Error ? error.message : String(error),
			});
			throw error;
		});

	// Use ctx.waitUntil if available, otherwise just fire and forget
	if (executionContext && typeof executionContext.waitUntil === "function") {
		console.log(
			"🔄 CUSTOM FIELD SYNC: Using ctx.waitUntil for background execution",
		);
		executionContext.waitUntil(fetchPromise);
	} else {
		console.log(
			"🔄 CUSTOM FIELD SYNC: No execution context, using fire-and-forget",
		);
		// Just trigger the promise without waiting
		fetchPromise.catch(() => {
			// Ignore errors in fire-and-forget mode
		});
	}

	console.log("🔄 CUSTOM FIELD SYNC: Request scheduled");
}
